#!/usr/bin/env python3
"""
Test database connection and check InstituteDocument table structure
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text, inspect
from config.session import engine, get_db
from Models.users import InstituteDocument, User
from sqlalchemy.orm import Session

def test_database_connection():
    """Test basic database connectivity"""
    try:
        with engine.connect() as connection:
            result = connection.execute(text("SELECT 1"))
            print("✅ Database connection successful")
            return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def check_table_exists(table_name):
    """Check if a table exists in the database"""
    try:
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        exists = table_name in tables
        print(f"📋 Table '{table_name}' exists: {exists}")
        if exists:
            columns = inspector.get_columns(table_name)
            print(f"📋 Table '{table_name}' columns:")
            for col in columns:
                print(f"   - {col['name']}: {col['type']} (nullable: {col['nullable']})")
        return exists
    except Exception as e:
        print(f"❌ Error checking table '{table_name}': {e}")
        return False

def check_foreign_key_constraints():
    """Check foreign key constraints for InstituteDocument table"""
    try:
        inspector = inspect(engine)
        if 'institute_documents' in inspector.get_table_names():
            fks = inspector.get_foreign_keys('institute_documents')
            print(f"📋 Foreign keys for 'institute_documents':")
            for fk in fks:
                print(f"   - {fk['constrained_columns']} -> {fk['referred_table']}.{fk['referred_columns']}")
        return True
    except Exception as e:
        print(f"❌ Error checking foreign keys: {e}")
        return False

def test_institute_document_creation():
    """Test creating an InstituteDocument record"""
    try:
        db = next(get_db())
        
        # First, check if there's at least one institute user
        institute_user = db.query(User).filter(User.user_type == 'institute').first()
        if not institute_user:
            print("❌ No institute user found in database")
            return False
        
        print(f"✅ Found institute user: {institute_user.id}")
        
        # Try to create a test document (but don't commit)
        import uuid
        test_doc = InstituteDocument(
            id=uuid.uuid4(),
            institute_id=institute_user.id,
            document_type='test',
            document_url='test/path.pdf',
            document_name='test.pdf',
            description='Test document',
            verified=False
        )
        
        db.add(test_doc)
        db.flush()  # This will check constraints without committing
        print("✅ InstituteDocument creation test passed")
        
        db.rollback()  # Don't actually save the test record
        return True
        
    except Exception as e:
        print(f"❌ InstituteDocument creation test failed: {e}")
        if 'db' in locals():
            db.rollback()
        return False
    finally:
        if 'db' in locals():
            db.close()

def create_missing_tables():
    """Create missing tables if they don't exist"""
    try:
        from Models.baseModel import BaseModel
        BaseModel.metadata.create_all(bind=engine)
        print("✅ Tables created/verified successfully")
        return True
    except Exception as e:
        print(f"❌ Error creating tables: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Starting database diagnostics...")
    print("=" * 60)
    
    # Test 1: Basic connection
    if not test_database_connection():
        return 1
    
    # Test 2: Check if tables exist
    print("\n📋 Checking table existence...")
    users_exists = check_table_exists('users')
    institute_docs_exists = check_table_exists('institute_documents')
    
    # Test 3: Create tables if missing
    if not users_exists or not institute_docs_exists:
        print("\n🔧 Creating missing tables...")
        if not create_missing_tables():
            return 1
        
        # Re-check after creation
        print("\n📋 Re-checking table existence after creation...")
        check_table_exists('users')
        check_table_exists('institute_documents')
    
    # Test 4: Check foreign key constraints
    print("\n📋 Checking foreign key constraints...")
    check_foreign_key_constraints()
    
    # Test 5: Test document creation
    print("\n🧪 Testing InstituteDocument creation...")
    if not test_institute_document_creation():
        return 1
    
    print("\n" + "=" * 60)
    print("✅ All database diagnostics passed!")
    print("📋 The database appears to be properly configured.")
    
    return 0

if __name__ == "__main__":
    exit(main())
