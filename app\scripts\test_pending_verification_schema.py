#!/usr/bin/env python3
"""
Test the updated pending verification function with InstituteListResponse schema
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastapi.testclient import TestClient
from main import app
from config.session import SessionLocal
from Models.users import User, InstituteProfile
from Cruds.Institute.Institute import get_institutes_pending_verification
import json

# Create test client
client = TestClient(app)

def test_pending_verification_function():
    """Test the CRUD function directly"""
    print("🧪 Testing get_institutes_pending_verification function...")
    
    db = SessionLocal()
    try:
        # Test the function with pagination
        result = get_institutes_pending_verification(db, skip=0, limit=10)
        
        print(f"📋 Function result type: {type(result)}")
        print(f"📋 Function result attributes: {dir(result)}")
        
        # Check if it has the expected InstituteListResponse structure
        expected_attrs = ['institutes', 'total', 'page', 'size', 'has_next', 'has_prev']
        
        for attr in expected_attrs:
            if hasattr(result, attr):
                value = getattr(result, attr)
                print(f"   ✅ {attr}: {type(value).__name__} = {value}")
            else:
                print(f"   ❌ Missing attribute: {attr}")
                return False
        
        # Check institutes list
        institutes = result.institutes
        print(f"📋 Institutes count: {len(institutes)}")
        
        if institutes:
            first_institute = institutes[0]
            print(f"📋 First institute structure:")
            for attr in ['id', 'username', 'institute_name', 'verification_status', 'created_at']:
                if hasattr(first_institute, attr):
                    value = getattr(first_institute, attr)
                    print(f"   - {attr}: {value}")
        
        # Check pagination calculations
        print(f"📋 Pagination info:")
        print(f"   - Total: {result.total}")
        print(f"   - Page: {result.page}")
        print(f"   - Size: {result.size}")
        print(f"   - Has next: {result.has_next}")
        print(f"   - Has prev: {result.has_prev}")
        
        return True
        
    except Exception as e:
        print(f"❌ Function test failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False
    finally:
        db.close()

def test_api_endpoint():
    """Test the API endpoint"""
    print("\n🧪 Testing API endpoint...")
    
    # Create admin user token
    from config.security import create_access_token
    
    db = SessionLocal()
    try:
        # Find an admin user or create one
        admin_user = db.query(User).filter(User.user_type == 'admin').first()
        
        if admin_user:
            token = create_access_token(data={"sub": admin_user.email})
            headers = {"Authorization": f"Bearer {token}"}
            
            # Test the new endpoint
            response = client.get("/api/admin/institutes/pending?skip=0&limit=5", headers=headers)
            
            print(f"📋 API response status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print("✅ API response successful!")
                
                # Check response structure
                expected_keys = ['institutes', 'total', 'page', 'size', 'has_next', 'has_prev']
                
                for key in expected_keys:
                    if key in data:
                        print(f"   ✅ {key}: {data[key]}")
                    else:
                        print(f"   ❌ Missing key: {key}")
                        return False
                
                # Check institutes array
                institutes = data.get('institutes', [])
                print(f"📋 API returned {len(institutes)} institutes")
                
                if institutes:
                    first_institute = institutes[0]
                    print(f"📋 First institute keys: {list(first_institute.keys())}")
                
                return True
            else:
                print(f"❌ API failed: {response.text}")
                return False
        else:
            print("⚠️  No admin user found, skipping API test")
            return True
            
    except Exception as e:
        print(f"❌ API test failed: {e}")
        return False
    finally:
        db.close()

def test_pagination_logic():
    """Test pagination calculations"""
    print("\n🧪 Testing pagination logic...")
    
    db = SessionLocal()
    try:
        # Test different pagination scenarios
        test_cases = [
            {"skip": 0, "limit": 5, "expected_page": 1},
            {"skip": 5, "limit": 5, "expected_page": 2},
            {"skip": 10, "limit": 3, "expected_page": 4},
        ]
        
        for case in test_cases:
            result = get_institutes_pending_verification(db, skip=case["skip"], limit=case["limit"])
            
            print(f"📋 Test case: skip={case['skip']}, limit={case['limit']}")
            print(f"   Expected page: {case['expected_page']}, Got: {result.page}")
            
            if result.page == case["expected_page"]:
                print(f"   ✅ Pagination correct")
            else:
                print(f"   ❌ Pagination incorrect")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Pagination test failed: {e}")
        return False
    finally:
        db.close()

def main():
    """Main test function"""
    print("🚀 Testing updated pending verification function with InstituteListResponse schema...")
    print("=" * 80)
    
    success_count = 0
    total_tests = 3
    
    # Test 1: Function structure
    if test_pending_verification_function():
        success_count += 1
    
    print("\n" + "-" * 50)
    
    # Test 2: API endpoint
    if test_api_endpoint():
        success_count += 1
    
    print("\n" + "-" * 50)
    
    # Test 3: Pagination logic
    if test_pagination_logic():
        success_count += 1
    
    print("\n" + "=" * 80)
    print(f"📊 Test Results: {success_count}/{total_tests} tests passed")
    
    if success_count == total_tests:
        print("✅ All tests passed!")
        print("\n📋 Summary:")
        print("   ✅ Function returns InstituteListResponse schema")
        print("   ✅ API endpoint works with new schema")
        print("   ✅ Pagination calculations are correct")
        print("\n🚀 Ready for frontend integration!")
        return 0
    else:
        print("❌ Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    exit(main())
