"""
Test Institute Verification Workflow

This module contains comprehensive tests for the institute verification system
including registration, profile completion, verification process, and access control.
"""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from datetime import datetime, timezone
import uuid

from main import app
from config.session import get_db
from Models.users import User, InstituteProfile, UserTypeEnum
from Schemas.Institute.Institute import InstituteRegistrationBase, InstituteVerificationUpdate


client = TestClient(app)


class TestInstituteVerificationWorkflow:
    """Test the complete institute verification workflow"""
    
    def setup_method(self):
        """Setup test data"""
        self.test_institute_data = {
            "username": "test_university",
            "email": "<EMAIL>",
            "mobile": "+**********",
            "password": "SecurePass123!",
            "country": "United States",
            "institute_name": "Test University",
            "description": "A test university for educational purposes",
            "institute_type": "university",
            "address": "123 University Ave",
            "city": "Test City",
            "state": "California",
            "postal_code": "90210",
            "website": "https://www.testuniversity.edu",
            "phone": "+1234567891",
            "established_year": 1995
        }
        
        self.admin_data = {
            "username": "admin_user",
            "email": "<EMAIL>",
            "mobile": "+1987654321",
            "password": "AdminPass123!",
            "country": "United States"
        }
    
    def test_institute_registration(self):
        """Test institute registration process"""
        response = client.post("/api/institutes/register", json=self.test_institute_data)
        
        assert response.status_code == 200
        data = response.json()
        
        # Check user data
        assert data["username"] == self.test_institute_data["username"]
        assert data["email"] == self.test_institute_data["email"]
        assert data["user_type"] == "institute"
        assert data["is_email_verified"] == False
        
        # Check institute profile
        assert data["institute_profile"] is not None
        profile = data["institute_profile"]
        assert profile["institute_name"] == self.test_institute_data["institute_name"]
        assert profile["verification_status"] == "pending"
        assert profile["is_verified"] == False
        
        return data["id"]  # Return institute ID for further tests
    
    def test_profile_completion_status(self):
        """Test profile completion status endpoint"""
        # First register an institute
        institute_id = self.test_institute_registration()
        
        # Login to get token
        login_response = client.post("/api/signin", json={
            "email": self.test_institute_data["email"],
            "password": self.test_institute_data["password"]
        })
        token = login_response.json()["access_token"]
        
        # Check profile status
        response = client.get(
            "/api/institutes/profile/status",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["profile_exists"] == True
        assert data["verification_status"] == "pending"
        assert data["is_verified"] == False
        assert "completion_percentage" in data
        assert "missing_fields" in data
        assert "required_fields" in data
    
    def test_unverified_institute_cannot_create_events(self):
        """Test that unverified institutes cannot create events"""
        # Register institute
        institute_id = self.test_institute_registration()
        
        # Login
        login_response = client.post("/api/signin", json={
            "email": self.test_institute_data["email"],
            "password": self.test_institute_data["password"]
        })
        token = login_response.json()["access_token"]
        
        # Try to create an event
        event_data = {
            "title": "Test Event",
            "description": "A test event",
            "start_datetime": "2024-12-01T10:00:00Z",
            "end_datetime": "2024-12-01T12:00:00Z"
        }
        
        response = client.post(
            "/api/institutes/events",
            json=event_data,
            headers={"Authorization": f"Bearer {token}"}
        )
        
        # Should be forbidden
        assert response.status_code == 403
        assert "verification" in response.json()["detail"].lower()
    
    def test_admin_verification_process(self):
        """Test admin verification of institute"""
        # Register institute
        institute_id = self.test_institute_registration()
        
        # Create admin user (this would normally be done through admin setup)
        # For testing, we'll simulate admin verification
        
        # Test getting pending verification list
        # Note: This would require admin authentication in real scenario
        
        # Test approving institute
        verification_data = {
            "verification_status": "approved",
            "verification_notes": "Institute verified successfully"
        }
        
        # This test would need proper admin authentication setup
        # For now, we'll test the logic without the full auth flow
        
        assert True  # Placeholder for admin verification test
    
    def test_verified_institute_can_create_events(self):
        """Test that verified institutes can create events"""
        # This test would require setting up a verified institute
        # and testing event creation
        
        assert True  # Placeholder for verified institute test
    
    def test_institute_dashboard(self):
        """Test institute dashboard endpoint"""
        # Register institute
        institute_id = self.test_institute_registration()
        
        # Login
        login_response = client.post("/api/signin", json={
            "email": self.test_institute_data["email"],
            "password": self.test_institute_data["password"]
        })
        token = login_response.json()["access_token"]
        
        # Get dashboard
        response = client.get(
            "/api/institutes/dashboard",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert "institute_name" in data
        assert "profile_status" in data
        assert "statistics" in data
        assert "next_actions" in data
        assert "account_created" in data
    
    def test_profile_validation(self):
        """Test profile validation logic"""
        from Schemas.Institute.Institute import InstituteProfileValidation
        
        # Test complete profile
        complete_profile = {
            "institute_name": "Test University",
            "description": "A test university",
            "address": "123 Test St",
            "city": "Test City",
            "state": "Test State",
            "phone": "+**********",
            "email": "<EMAIL>",
            "established_year": 2000,
            "institute_type": "university"
        }
        
        result = InstituteProfileValidation.validate_profile_completeness(complete_profile)
        assert result["is_complete"] == True
        assert result["completion_percentage"] == 100.0
        assert len(result["missing_fields"]) == 0
        
        # Test incomplete profile
        incomplete_profile = {
            "institute_name": "Test University",
            "description": "",  # Missing
            "address": "123 Test St",
            # Missing city, state, phone, email, established_year, institute_type
        }
        
        result = InstituteProfileValidation.validate_profile_completeness(incomplete_profile)
        assert result["is_complete"] == False
        assert result["completion_percentage"] < 100.0
        assert len(result["missing_fields"]) > 0
    
    def test_schema_validation(self):
        """Test institute registration schema validation"""
        from Schemas.Institute.Institute import InstituteRegistrationBase
        
        # Test valid data
        valid_data = self.test_institute_data.copy()
        schema = InstituteRegistrationBase(**valid_data)
        assert schema.username == valid_data["username"].lower()
        assert schema.institute_name == valid_data["institute_name"]
        
        # Test invalid username
        invalid_data = valid_data.copy()
        invalid_data["username"] = "admin"  # Reserved username
        
        with pytest.raises(ValueError, match="reserved"):
            InstituteRegistrationBase(**invalid_data)
        
        # Test weak password
        invalid_data = valid_data.copy()
        invalid_data["password"] = "weak"
        
        with pytest.raises(ValueError, match="Password must"):
            InstituteRegistrationBase(**invalid_data)
        
        # Test invalid phone number
        invalid_data = valid_data.copy()
        invalid_data["mobile"] = "invalid-phone"
        
        with pytest.raises(ValueError, match="Invalid phone number"):
            InstituteRegistrationBase(**invalid_data)


class TestInstitutePermissions:
    """Test institute permission system"""
    
    def test_verification_middleware(self):
        """Test verification middleware functionality"""
        # This would test the require_verified_institute dependency
        assert True  # Placeholder
    
    def test_profile_completion_middleware(self):
        """Test profile completion middleware"""
        # This would test the require_institute_with_profile dependency
        assert True  # Placeholder


class TestAdminInstituteManagement:
    """Test admin institute management features"""
    
    def test_bulk_verification(self):
        """Test bulk verification of institutes"""
        assert True  # Placeholder
    
    def test_verification_queue(self):
        """Test verification queue with filtering"""
        assert True  # Placeholder


if __name__ == "__main__":
    pytest.main([__file__])
