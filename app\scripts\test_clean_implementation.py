#!/usr/bin/env python3
"""
Test the clean implementation with proper CRUD operations
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastapi.testclient import TestClient
from main import app

# Create test client
client = TestClient(app)

def test_app_loads_successfully():
    """Test that the app loads without any import or syntax errors"""
    print("🧪 Testing app loads successfully...")
    
    try:
        # Try to access the health endpoint or docs
        response = client.get("/docs")
        if response.status_code == 200:
            print("✅ App loads successfully!")
            print("✅ No import errors")
            print("✅ No syntax errors")
            return True
        else:
            print(f"❌ App docs not accessible: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ App failed to load: {e}")
        return False

def test_clean_endpoint_structure():
    """Test that endpoints have clean structure"""
    print("\n🧪 Testing clean endpoint structure...")
    
    try:
        # Get OpenAPI schema
        response = client.get("/openapi.json")
        if response.status_code == 200:
            openapi_data = response.json()
            paths = openapi_data.get("paths", {})
            
            # Check profile endpoint
            profile_path = "/api/institutes/profile"
            if profile_path in paths:
                put_method = paths[profile_path].get("put", {})
                params = put_method.get("parameters", [])
                request_body = put_method.get("requestBody", {})
                
                # Should have clean JSON request body, no form parameters
                if "application/json" in request_body.get("content", {}):
                    print("✅ Profile endpoint uses clean JSON request")
                    
                    # Check with-documents endpoint
                    docs_path = "/api/institutes/profile/with-documents"
                    if docs_path in paths:
                        docs_put = paths[docs_path].get("put", {})
                        docs_request_body = docs_put.get("requestBody", {})
                        
                        # Should have multipart content but clean structure
                        if "multipart/form-data" in docs_request_body.get("content", {}):
                            print("✅ With-documents endpoint uses multipart for files")
                            return True
                        else:
                            print("❌ With-documents endpoint missing multipart")
                            return False
                    else:
                        print("❌ With-documents endpoint not found")
                        return False
                else:
                    print("❌ Profile endpoint not using JSON")
                    return False
            else:
                print("❌ Profile endpoint not found")
                return False
        else:
            print(f"❌ Could not get OpenAPI schema: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error checking endpoint structure: {e}")
        return False

def test_crud_separation():
    """Test that CRUD operations are properly separated"""
    print("\n🧪 Testing CRUD separation...")
    
    try:
        # Check if CRUD functions exist
        from Cruds.Institute.Institute import (
            update_institute_profile,
            update_institute_profile_with_documents
        )
        
        print("✅ CRUD functions imported successfully")
        print("✅ update_institute_profile exists")
        print("✅ update_institute_profile_with_documents exists")
        
        # Check if route imports CRUD functions
        from Routes.Institute.Institute import router
        print("✅ Route imports work correctly")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error checking CRUD separation: {e}")
        return False

def test_pydantic_models():
    """Test that Pydantic models are properly defined"""
    print("\n🧪 Testing Pydantic models...")
    
    try:
        from Schemas.Institute.Institute import (
            InstituteProfileUpdate,
            InstituteProfileWithDocumentsRequest,
            InstituteDocumentMetadata
        )
        
        print("✅ InstituteProfileUpdate model exists")
        print("✅ InstituteProfileWithDocumentsRequest model exists") 
        print("✅ InstituteDocumentMetadata model exists")
        
        # Test model instantiation
        profile_update = InstituteProfileUpdate(institute_name="Test University")
        print("✅ InstituteProfileUpdate can be instantiated")
        
        doc_meta = InstituteDocumentMetadata(document_type="accreditation")
        print("✅ InstituteDocumentMetadata can be instantiated")
        
        profile_with_docs = InstituteProfileWithDocumentsRequest(
            institute_name="Test University",
            document_metadata=[doc_meta]
        )
        print("✅ InstituteProfileWithDocumentsRequest can be instantiated")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing Pydantic models: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Testing clean implementation...")
    print("=" * 60)
    
    success_count = 0
    total_tests = 4
    
    # Test 1: App loads
    if test_app_loads_successfully():
        success_count += 1
    
    # Test 2: Clean endpoint structure
    if test_clean_endpoint_structure():
        success_count += 1
    
    # Test 3: CRUD separation
    if test_crud_separation():
        success_count += 1
    
    # Test 4: Pydantic models
    if test_pydantic_models():
        success_count += 1
    
    print("\n" + "=" * 60)
    if success_count == total_tests:
        print("✅ SUCCESS: Clean implementation achieved!")
        print("🎉 Benefits:")
        print("   ✅ Clean separation of concerns")
        print("   ✅ CRUD operations in CRUD files")
        print("   ✅ Routes only handle HTTP concerns")
        print("   ✅ Proper Pydantic models")
        print("   ✅ No messy Form() parameters")
        print("   ✅ Maintainable and testable code")
        return 0
    else:
        print(f"❌ {total_tests - success_count} test(s) failed")
        return 1

if __name__ == "__main__":
    exit(main())
