#!/usr/bin/env python3
"""
Test the new clean profile update endpoint that uses proper Pydantic schemas
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastapi.testclient import Test<PERSON>lient
from main import app
from config.session import SessionLocal
from Models.users import User

# Create test client
client = TestClient(app)

def get_test_user_token():
    """Get auth token for test user"""
    from config.security import create_access_token
    
    db = SessionLocal()
    try:
        test_email = "<EMAIL>"
        user = db.query(User).filter(User.email == test_email).first()
        if user:
            token = create_access_token(data={"sub": test_email})
            return token, user.id
    finally:
        db.close()
    return None, None

def test_clean_profile_update():
    """Test the new clean JSON profile update endpoint"""
    print("🧪 Testing clean profile update endpoint (JSON only)...")
    
    token, user_id = get_test_user_token()
    if not token:
        print("❌ Could not get test user token")
        return False
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # Test data using proper Pydantic schema structure
    profile_data = {
        "institute_name": "Clean Test University",
        "description": "A university updated using clean Pydantic schemas",
        "city": "Schema City",
        "state": "Clean State",
        "institute_type": "university",
        "established_year": 2000,
        "website": "https://clean.university.edu"
    }
    
    # Test the clean JSON endpoint
    response = client.put("/api/institutes/profile", headers=headers, json=profile_data)
    
    print(f"📋 Response status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        profile = data.get('profile', {})
        
        print("✅ Clean profile update successful!")
        print(f"📋 Institute name: {profile.get('institute_name')}")
        print(f"📋 Description: {profile.get('description')}")
        print(f"📋 City: {profile.get('city')}")
        print(f"📋 Institute type: {profile.get('institute_type')}")
        print(f"📋 Established year: {profile.get('established_year')}")
        print(f"📋 Website: {profile.get('website')}")
        
        # Verify the data was updated correctly
        if (profile.get('institute_name') == profile_data['institute_name'] and
            profile.get('description') == profile_data['description'] and
            profile.get('city') == profile_data['city']):
            print("✅ All fields updated correctly!")
            return True
        else:
            print("❌ Some fields were not updated correctly")
            return False
    else:
        print(f"❌ Clean profile update failed: {response.status_code}")
        print(f"Response: {response.text}")
        return False

def test_validation_errors():
    """Test that the endpoint properly validates input"""
    print("\n🧪 Testing validation errors...")
    
    token, user_id = get_test_user_token()
    if not token:
        print("❌ Could not get test user token")
        return False
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # Test invalid institute type
    invalid_data = {
        "institute_name": "Test University",
        "institute_type": "invalid_type"  # This should fail validation
    }
    
    response = client.put("/api/institutes/profile", headers=headers, json=invalid_data)
    
    if response.status_code == 422:  # Validation error
        print("✅ Validation error properly caught for invalid institute type")
        return True
    else:
        print(f"❌ Expected validation error (422), got {response.status_code}")
        return False

def main():
    """Main test function"""
    print("🚀 Testing clean profile update endpoint...")
    print("=" * 60)
    
    success_count = 0
    total_tests = 2
    
    # Test 1: Clean profile update
    if test_clean_profile_update():
        success_count += 1
    
    print("\n" + "-" * 40)
    
    # Test 2: Validation errors
    if test_validation_errors():
        success_count += 1
    
    print("\n" + "=" * 60)
    if success_count == total_tests:
        print("✅ SUCCESS: Clean profile endpoint works perfectly!")
        print("🎉 Benefits of the new approach:")
        print("   ✅ Uses proper Pydantic schemas")
        print("   ✅ Clean JSON API (no messy Form parameters)")
        print("   ✅ Automatic validation")
        print("   ✅ Type safety")
        print("   ✅ Easy to test and maintain")
        print("   ✅ Follows FastAPI best practices")
        return 0
    else:
        print("❌ Some tests failed")
        return 1

if __name__ == "__main__":
    exit(main())
