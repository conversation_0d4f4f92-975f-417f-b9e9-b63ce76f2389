#!/usr/bin/env python3
"""
Test institute profile creation and document handling
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.session import SessionLocal
from Models.users import User, UserTypeEnum, InstituteProfile, InstituteDocument
from Schemas.Institute.Institute import InstituteProfileUpdate
from Cruds.Institute.Institute import update_institute_profile, get_institute_by_id
import uuid

def test_profile_creation():
    """Test creating and updating an institute profile"""
    db = SessionLocal()
    try:
        # Find our test user
        test_email = "<EMAIL>"
        user = db.query(User).filter(User.email == test_email).first()
        
        if not user:
            print("❌ Test user not found")
            return False
        
        print(f"✅ Found test user: {user.id}")
        
        # Check if profile exists
        profile = db.query(InstituteProfile).filter(InstituteProfile.user_id == user.id).first()
        print(f"📋 Profile exists: {profile is not None}")
        
        if profile:
            print(f"📋 Current profile name: {profile.institute_name}")
            print(f"📋 Current profile description: {profile.description}")
        
        # Test profile update
        profile_update = InstituteProfileUpdate(
            institute_name="Test University Updated",
            description="A test educational institution",
            city="Test City",
            state="Test State"
        )
        
        print("🧪 Testing profile update...")
        result = update_institute_profile(db, user.id, profile_update)
        
        print(f"✅ Profile update successful!")
        print(f"📋 Updated institute name: {result.profile.institute_name}")
        print(f"📋 Updated description: {result.profile.description}")
        
        # Check documents
        documents = db.query(InstituteDocument).filter(InstituteDocument.institute_id == user.id).all()
        print(f"📋 Documents in database: {len(documents)}")
        
        for doc in documents:
            print(f"   - {doc.document_name} ({doc.document_type}) - Created: {doc.created_at}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False
    finally:
        db.close()

def test_get_institute_by_id():
    """Test the get_institute_by_id function"""
    db = SessionLocal()
    try:
        # Find our test user
        test_email = "<EMAIL>"
        user = db.query(User).filter(User.email == test_email).first()
        
        if not user:
            print("❌ Test user not found")
            return False
        
        print(f"🧪 Testing get_institute_by_id for user: {user.id}")
        
        result = get_institute_by_id(db, user.id)
        
        print(f"✅ get_institute_by_id successful!")
        print(f"📋 Institute name: {result.profile.institute_name}")
        print(f"📋 Documents count: {len(result.profile.documents)}")

        for doc in result.profile.documents:
            print(f"   - {doc.document_name} ({doc.document_type})")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in get_institute_by_id: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False
    finally:
        db.close()

def main():
    """Main test function"""
    print("🚀 Testing institute profile creation and retrieval...")
    print("=" * 60)
    
    success_count = 0
    total_tests = 2
    
    # Test 1: Profile creation/update
    if test_profile_creation():
        success_count += 1
    
    print("\n" + "-" * 40)
    
    # Test 2: Profile retrieval
    if test_get_institute_by_id():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {success_count}/{total_tests} tests passed")
    
    return 0 if success_count == total_tests else 1

if __name__ == "__main__":
    exit(main())
