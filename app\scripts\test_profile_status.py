#!/usr/bin/env python3
"""
Test the enhanced institute profile status endpoint
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastapi.testclient import TestClient
from main import app
from config.session import SessionLocal
from Models.users import User, InstituteProfile
import json

# Create test client
client = TestClient(app)

def get_test_user_token():
    """Get auth token for test user"""
    from config.security import create_access_token
    
    db = SessionLocal()
    try:
        test_email = "<EMAIL>"
        user = db.query(User).filter(User.email == test_email).first()
        if user:
            token = create_access_token(data={"sub": test_email})
            return token, user.id
    finally:
        db.close()
    return None, None

def test_profile_status_endpoint():
    """Test the profile status endpoint with different scenarios"""
    token, user_id = get_test_user_token()
    if not token:
        print("❌ Could not get test user token")
        return False
    
    headers = {"Authorization": f"Bearer {token}"}
    
    print("🧪 Testing profile status endpoint...")
    
    # Test current status
    response = client.get("/api/institutes/profile/status", headers=headers)
    
    print(f"📋 Response status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print("✅ Profile status retrieved successfully!")
        print(f"📋 Profile exists: {data.get('profile_exists')}")
        print(f"📋 Profile complete: {data.get('profile_complete')}")
        print(f"📋 Verification status: {data.get('verification_status')}")
        print(f"📋 Completion percentage: {data.get('completion_percentage')}%")
        
        # Print UI actions
        ui_actions = data.get('ui_actions', {})
        print("\n🎯 UI Actions:")
        
        primary_btn = ui_actions.get('primary_button')
        if primary_btn:
            print(f"   Primary Button: '{primary_btn.get('text')}' ({'enabled' if primary_btn.get('enabled') else 'disabled'})")
            if not primary_btn.get('enabled'):
                print(f"   Disabled reason: {primary_btn.get('disabled_reason')}")
        
        secondary_btn = ui_actions.get('secondary_button')
        if secondary_btn:
            print(f"   Secondary Button: '{secondary_btn.get('text')}' ({'enabled' if secondary_btn.get('enabled') else 'disabled'})")
            if not secondary_btn.get('enabled'):
                print(f"   Disabled reason: {secondary_btn.get('disabled_reason')}")
        else:
            print("   Secondary Button: None")
        
        status_msg = ui_actions.get('status_message')
        if status_msg:
            print(f"   Status Message: {status_msg}")
        
        # Print missing fields if any
        missing_fields = data.get('missing_fields', [])
        if missing_fields:
            print(f"\n⚠️  Missing fields: {', '.join(missing_fields)}")
        
        return True
    else:
        print(f"❌ Request failed: {response.text}")
        return False

def test_different_profile_states():
    """Test by modifying profile state and checking responses"""
    token, user_id = get_test_user_token()
    if not token:
        print("❌ Could not get test user token")
        return False
    
    db = SessionLocal()
    try:
        # Get the user's profile
        user = db.query(User).filter(User.id == user_id).first()
        if not user or not user.institute_profile:
            print("❌ No profile found for testing")
            return False
        
        profile = user.institute_profile
        original_status = profile.verification_status
        
        print("🧪 Testing different verification states...")
        
        # Test different states
        test_states = [
            ("pending", "Profile pending verification"),
            ("under_review", "Profile under review"),
            ("verified", "Profile verified"),
            ("rejected", "Profile rejected")
        ]
        
        headers = {"Authorization": f"Bearer {token}"}
        
        for status, description in test_states:
            print(f"\n--- Testing {description} ---")
            
            # Update profile status
            profile.verification_status = status
            if status == "rejected":
                profile.verification_notes = "Please update your institute description with more details."
            else:
                profile.verification_notes = None
            
            db.commit()
            
            # Test the endpoint
            response = client.get("/api/institutes/profile/status", headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                ui_actions = data.get('ui_actions', {})
                
                print(f"   Status: {data.get('verification_status')}")
                print(f"   Primary Button: {ui_actions.get('primary_button', {}).get('text')} ({'enabled' if ui_actions.get('primary_button', {}).get('enabled') else 'disabled'})")
                
                secondary_btn = ui_actions.get('secondary_button')
                if secondary_btn:
                    print(f"   Secondary Button: {secondary_btn.get('text')} ({'enabled' if secondary_btn.get('enabled') else 'disabled'})")
                else:
                    print("   Secondary Button: None")
                
                status_msg = ui_actions.get('status_message')
                if status_msg:
                    print(f"   Message: {status_msg}")
            else:
                print(f"   ❌ Request failed: {response.text}")
        
        # Restore original status
        profile.verification_status = original_status
        profile.verification_notes = None
        db.commit()
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing profile states: {e}")
        db.rollback()
        return False
    finally:
        db.close()

def main():
    """Main test function"""
    print("🚀 Testing enhanced institute profile status endpoint...")
    print("=" * 60)
    
    success_count = 0
    total_tests = 2
    
    # Test 1: Basic status endpoint
    if test_profile_status_endpoint():
        success_count += 1
    
    print("\n" + "-" * 40)
    
    # Test 2: Different profile states
    if test_different_profile_states():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {success_count}/{total_tests} tests passed")
    
    if success_count == total_tests:
        print("✅ All tests passed! The enhanced profile status endpoint is working correctly.")
        print("\n📋 Frontend developers can now use the ui_actions object to:")
        print("   - Determine button text and states")
        print("   - Show appropriate status messages")
        print("   - Handle different verification states")
        print("   - Provide better user experience")
        return 0
    else:
        print("❌ Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    exit(main())
