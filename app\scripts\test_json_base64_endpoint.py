#!/usr/bin/env python3
"""
Test the clean JSON endpoint with base64 documents
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastapi.testclient import TestClient
from main import app

# Create test client
client = TestClient(app)

def test_endpoint_structure():
    """Test that the endpoint has clean JSON structure"""
    print("🧪 Testing endpoint structure...")
    
    try:
        # Get OpenAPI schema
        response = client.get("/openapi.json")
        if response.status_code == 200:
            openapi_data = response.json()
            paths = openapi_data.get("paths", {})
            
            # Check with-documents endpoint
            docs_path = "/api/institutes/profile/with-documents"
            if docs_path in paths:
                put_method = paths[docs_path].get("put", {})
                request_body = put_method.get("requestBody", {})
                content = request_body.get("content", {})
                
                # Should have application/json content type (not multipart)
                if "application/json" in content:
                    schema_ref = content["application/json"].get("schema", {}).get("$ref", "")
                    if "InstituteProfileWithDocumentsRequest" in schema_ref:
                        print("✅ Endpoint uses clean JSON with InstituteProfileWithDocumentsRequest")
                        return True
                    else:
                        print(f"❌ Endpoint schema: {schema_ref}")
                        return False
                else:
                    print(f"❌ Endpoint content types: {list(content.keys())}")
                    return False
            else:
                print("❌ With-documents endpoint not found")
                return False
        else:
            print(f"❌ Could not get OpenAPI schema: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error checking endpoint structure: {e}")
        return False

def test_schema_structure():
    """Test that the schema has clean structure"""
    print("\n🧪 Testing schema structure...")
    
    try:
        # Get OpenAPI schema
        response = client.get("/openapi.json")
        if response.status_code == 200:
            openapi_data = response.json()
            schemas = openapi_data.get("components", {}).get("schemas", {})
            
            # Check InstituteProfileWithDocumentsRequest schema
            if "InstituteProfileWithDocumentsRequest" in schemas:
                schema = schemas["InstituteProfileWithDocumentsRequest"]
                properties = schema.get("properties", {})
                
                # Should have profile fields
                profile_fields = ["institute_name", "description", "city", "state", "institute_type"]
                has_profile_fields = all(field in properties for field in profile_fields)
                
                # Should have documents field
                has_documents = "documents" in properties
                
                if has_profile_fields and has_documents:
                    print("✅ Schema has profile fields and documents array")
                    
                    # Check DocumentData schema
                    if "DocumentData" in schemas:
                        doc_schema = schemas["DocumentData"]
                        doc_properties = doc_schema.get("properties", {})
                        
                        expected_doc_fields = ["filename", "content_type", "data", "document_type", "description"]
                        has_doc_fields = all(field in doc_properties for field in expected_doc_fields)
                        
                        if has_doc_fields:
                            print("✅ DocumentData schema has all required fields")
                            return True
                        else:
                            print(f"❌ DocumentData missing fields")
                            return False
                    else:
                        print("❌ DocumentData schema not found")
                        return False
                else:
                    print(f"❌ Profile fields: {has_profile_fields}, Documents: {has_documents}")
                    return False
            else:
                print("❌ InstituteProfileWithDocumentsRequest schema not found")
                return False
        else:
            print(f"❌ Could not get OpenAPI schema: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error checking schema structure: {e}")
        return False

def test_pydantic_models():
    """Test that Pydantic models work correctly"""
    print("\n🧪 Testing Pydantic models...")
    
    try:
        from Schemas.Institute.Institute import InstituteProfileWithDocumentsRequest, DocumentData
        
        # Test DocumentData model
        doc_data = DocumentData(
            filename="test.pdf",
            content_type="application/pdf",
            data="dGVzdCBkYXRh",  # base64 for "test data"
            document_type="accreditation",
            description="Test document"
        )
        print("✅ DocumentData model works")
        
        # Test InstituteProfileWithDocumentsRequest model
        request_data = InstituteProfileWithDocumentsRequest(
            institute_name="Test University",
            description="Test description",
            city="Test City",
            institute_type="university",
            documents=[doc_data]
        )
        print("✅ InstituteProfileWithDocumentsRequest model works")
        
        # Test JSON serialization
        json_data = request_data.model_dump()
        if "documents" in json_data and len(json_data["documents"]) == 1:
            print("✅ Model serialization works")
            return True
        else:
            print("❌ Model serialization failed")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing Pydantic models: {e}")
        return False

def test_crud_function():
    """Test that CRUD function exists and has correct signature"""
    print("\n🧪 Testing CRUD function...")
    
    try:
        from Cruds.Institute.Institute import update_institute_profile_with_documents
        print("✅ CRUD function exists")
        
        # Check function signature
        import inspect
        sig = inspect.signature(update_institute_profile_with_documents)
        params = list(sig.parameters.keys())
        
        expected_params = ["db", "institute_id", "request"]
        if all(param in params for param in expected_params):
            print("✅ CRUD function has correct parameters")
            return True
        else:
            print(f"❌ CRUD function parameters: {params}")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error checking CRUD function: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Testing clean JSON endpoint with base64 documents...")
    print("=" * 60)
    print("APPROACH: Pure JSON with base64 encoded documents")
    print("BENEFITS: Clean, simple, type-safe, easy to use")
    print("=" * 60)
    
    success_count = 0
    total_tests = 4
    
    # Test 1: Endpoint structure
    if test_endpoint_structure():
        success_count += 1
    
    # Test 2: Schema structure
    if test_schema_structure():
        success_count += 1
    
    # Test 3: Pydantic models
    if test_pydantic_models():
        success_count += 1
    
    # Test 4: CRUD function
    if test_crud_function():
        success_count += 1
    
    print("\n" + "=" * 60)
    if success_count == total_tests:
        print("✅ SUCCESS: Clean JSON endpoint is perfect!")
        print("🎉 Achievements:")
        print("   ✅ Pure JSON API (no multipart complexity)")
        print("   ✅ Single request body with everything")
        print("   ✅ Base64 encoded documents")
        print("   ✅ Clean Pydantic models")
        print("   ✅ Type safety and validation")
        print("   ✅ Easy to use and test")
        print("   ✅ Standard REST API approach")
        return 0
    else:
        print(f"❌ {total_tests - success_count} test(s) failed")
        return 1

if __name__ == "__main__":
    exit(main())
