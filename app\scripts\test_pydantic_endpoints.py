#!/usr/bin/env python3
"""
Test that all institute endpoints now use proper Pydantic models
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastapi.testclient import TestClient
from main import app
import json

# Create test client
client = TestClient(app)

def test_profile_endpoint_uses_pydantic():
    """Test that the profile endpoint uses Pydantic models"""
    print("🧪 Testing profile endpoint uses Pydantic models...")
    
    try:
        # Get OpenAPI schema
        response = client.get("/openapi.json")
        if response.status_code == 200:
            openapi_data = response.json()
            
            # Check the profile endpoint
            profile_path = "/api/institutes/profile"
            if profile_path in openapi_data["paths"]:
                put_method = openapi_data["paths"][profile_path].get("put", {})
                request_body = put_method.get("requestBody", {})
                content = request_body.get("content", {})
                
                # Should have application/json content type (Pydantic model)
                if "application/json" in content:
                    schema_ref = content["application/json"].get("schema", {}).get("$ref", "")
                    if "InstituteProfileUpdate" in schema_ref:
                        print("✅ Profile endpoint uses InstituteProfileUpdate Pydantic model")
                        return True
                    else:
                        print(f"❌ Profile endpoint schema: {schema_ref}")
                        return False
                else:
                    print(f"❌ Profile endpoint content types: {list(content.keys())}")
                    return False
            else:
                print("❌ Profile endpoint not found")
                return False
        else:
            print(f"❌ Could not get OpenAPI schema: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error checking profile endpoint: {e}")
        return False

def test_with_documents_endpoint_structure():
    """Test that the with-documents endpoint has proper structure"""
    print("\n🧪 Testing with-documents endpoint structure...")
    
    try:
        # Get OpenAPI schema
        response = client.get("/openapi.json")
        if response.status_code == 200:
            openapi_data = response.json()
            
            # Check the with-documents endpoint
            docs_path = "/api/institutes/profile/with-documents"
            if docs_path in openapi_data["paths"]:
                put_method = openapi_data["paths"][docs_path].get("put", {})
                request_body = put_method.get("requestBody", {})
                content = request_body.get("content", {})
                
                # Should have multipart/form-data content type
                if "multipart/form-data" in content:
                    schema = content["multipart/form-data"].get("schema", {})
                    properties = schema.get("properties", {})
                    
                    # Check for profile_data field (JSON string)
                    if "profile_data" in properties:
                        profile_data_prop = properties["profile_data"]
                        if profile_data_prop.get("type") == "string":
                            print("✅ With-documents endpoint uses profile_data as JSON string")
                            
                            # Check for document_files field
                            if "document_files" in properties:
                                print("✅ With-documents endpoint has document_files field")
                                return True
                            else:
                                print("❌ With-documents endpoint missing document_files field")
                                return False
                        else:
                            print(f"❌ profile_data type: {profile_data_prop.get('type')}")
                            return False
                    else:
                        print(f"❌ With-documents endpoint properties: {list(properties.keys())}")
                        return False
                else:
                    print(f"❌ With-documents endpoint content types: {list(content.keys())}")
                    return False
            else:
                print("❌ With-documents endpoint not found")
                return False
        else:
            print(f"❌ Could not get OpenAPI schema: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error checking with-documents endpoint: {e}")
        return False

def test_pydantic_schemas_exist():
    """Test that the required Pydantic schemas exist"""
    print("\n🧪 Testing Pydantic schemas exist...")
    
    try:
        # Get OpenAPI schema
        response = client.get("/openapi.json")
        if response.status_code == 200:
            openapi_data = response.json()
            components = openapi_data.get("components", {})
            schemas = components.get("schemas", {})
            
            required_schemas = [
                "InstituteProfileUpdate",
                "InstituteProfileWithDocumentsRequest",
                "InstituteDocumentMetadata",
                "InstituteDetailedOut"
            ]
            
            missing_schemas = []
            for schema_name in required_schemas:
                if schema_name not in schemas:
                    missing_schemas.append(schema_name)
                else:
                    print(f"✅ Schema exists: {schema_name}")
            
            if not missing_schemas:
                print("✅ All required Pydantic schemas exist!")
                return True
            else:
                print(f"❌ Missing schemas: {missing_schemas}")
                return False
        else:
            print(f"❌ Could not get OpenAPI schema: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error checking schemas: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Testing that all endpoints use proper Pydantic models...")
    print("=" * 60)
    
    success_count = 0
    total_tests = 3
    
    # Test 1: Profile endpoint uses Pydantic
    if test_profile_endpoint_uses_pydantic():
        success_count += 1
    
    # Test 2: With-documents endpoint structure
    if test_with_documents_endpoint_structure():
        success_count += 1
    
    # Test 3: Pydantic schemas exist
    if test_pydantic_schemas_exist():
        success_count += 1
    
    print("\n" + "=" * 60)
    if success_count == total_tests:
        print("✅ SUCCESS: All endpoints now use proper Pydantic models!")
        print("🎉 Benefits achieved:")
        print("   ✅ Type safety and validation")
        print("   ✅ Automatic API documentation")
        print("   ✅ Clean, maintainable code")
        print("   ✅ FastAPI best practices followed")
        print("   ✅ No more messy Form parameters")
        return 0
    else:
        print(f"❌ {total_tests - success_count} test(s) failed")
        return 1

if __name__ == "__main__":
    exit(main())
