#!/usr/bin/env python3
"""
Test the admin endpoint response format to help debug frontend issues
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastapi.testclient import Test<PERSON>lient
from main import app
from config.session import SessionLocal
from Models.users import User, UserTypeEnum
import json

# Create test client
client = TestClient(app)

def create_admin_user():
    """Create a test admin user"""
    db = SessionLocal()
    try:
        # Check if admin user exists
        admin_email = "<EMAIL>"
        admin_user = db.query(User).filter(User.email == admin_email).first()
        
        if not admin_user:
            # Create admin user
            from config.security import get_password_hash
            import uuid
            
            admin_user = User(
                id=uuid.uuid4(),
                username="admin",
                email=admin_email,
                mobile="+1234567890",
                password_hash=get_password_hash("admin123"),
                country="US",
                user_type=UserTypeEnum.admin,
                is_email_verified=True,
                is_mobile_verified=True
            )
            
            db.add(admin_user)
            db.commit()
            db.refresh(admin_user)
            print(f"✅ Created admin user: {admin_user.id}")
        else:
            print(f"✅ Using existing admin user: {admin_user.id}")
        
        # Generate auth token
        from config.security import create_access_token
        token = create_access_token(data={"sub": admin_email})
        
        return token, admin_user.id
        
    except Exception as e:
        print(f"❌ Error creating admin user: {e}")
        db.rollback()
        return None, None
    finally:
        db.close()

def test_admin_endpoint_response():
    """Test what the admin endpoint actually returns"""
    print("🧪 Testing admin endpoint response format...")
    
    # Create admin user and get token
    token, user_id = create_admin_user()
    if not token:
        print("❌ Could not create admin user")
        return False
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # Test the endpoint
    response = client.get("/api/admin/institutes/pending", headers=headers)
    
    print(f"📋 Response status: {response.status_code}")
    print(f"📋 Response headers: {dict(response.headers)}")
    
    if response.status_code == 200:
        try:
            data = response.json()
            print("✅ Response is valid JSON")
            print(f"📋 Response type: {type(data)}")
            
            if isinstance(data, list):
                print(f"📋 Response is a list with {len(data)} items")
                
                if data:
                    print("📋 First item structure:")
                    first_item = data[0]
                    for key, value in first_item.items():
                        print(f"   - {key}: {type(value).__name__} = {value}")
                else:
                    print("📋 Empty list (no pending institutes)")
                
                # This is what the frontend expects
                print("\n✅ Response format is correct for frontend:")
                print("   - Array of institute objects")
                print("   - Each object has institute details")
                
                return True
            else:
                print(f"❌ Expected list, got {type(data)}")
                print(f"📋 Response content: {data}")
                return False
                
        except json.JSONDecodeError as e:
            print(f"❌ Response is not valid JSON: {e}")
            print(f"📋 Raw response: {response.text}")
            return False
    else:
        print(f"❌ Request failed with status {response.status_code}")
        print(f"📋 Response: {response.text}")
        return False

def test_error_response_format():
    """Test error response format"""
    print("\n🧪 Testing error response format...")
    
    # Test with invalid token
    headers = {"Authorization": "Bearer invalid_token"}
    response = client.get("/api/admin/institutes/pending", headers=headers)
    
    print(f"📋 Error response status: {response.status_code}")
    
    if response.status_code in [401, 403]:
        try:
            error_data = response.json()
            print("✅ Error response is valid JSON")
            print(f"📋 Error structure:")
            for key, value in error_data.items():
                print(f"   - {key}: {type(value).__name__} = {value}")
            
            # Check if it has the problematic 'detail' key
            if 'detail' in error_data:
                detail = error_data['detail']
                print(f"\n⚠️  'detail' field type: {type(detail)}")
                print(f"⚠️  'detail' content: {detail}")
                
                if isinstance(detail, dict):
                    print("❌ 'detail' is an object - this causes React error!")
                    print("💡 Frontend should check if detail is string before rendering")
                elif isinstance(detail, str):
                    print("✅ 'detail' is a string - safe for React")
            
            return True
            
        except json.JSONDecodeError as e:
            print(f"❌ Error response is not valid JSON: {e}")
            return False
    else:
        print(f"⚠️  Unexpected error status: {response.status_code}")
        return False

def main():
    """Main test function"""
    print("🚀 Testing admin endpoint response format for frontend compatibility...")
    print("=" * 70)
    
    success_count = 0
    total_tests = 2
    
    # Test 1: Successful response format
    if test_admin_endpoint_response():
        success_count += 1
    
    # Test 2: Error response format
    if test_error_response_format():
        success_count += 1
    
    print("\n" + "=" * 70)
    print(f"📊 Test Results: {success_count}/{total_tests} tests passed")
    
    if success_count == total_tests:
        print("✅ Admin endpoint response format is correct!")
        print("\n📋 Frontend Integration Notes:")
        print("   ✅ Success response: Array of institute objects")
        print("   ✅ Error response: Object with 'detail' string")
        print("   💡 Make sure frontend handles error.detail as string")
        print("\n🚀 Endpoint ready: GET /api/admin/institutes/pending")
        return 0
    else:
        print("❌ Some issues found. Check the output above for details.")
        return 1

if __name__ == "__main__":
    exit(main())
