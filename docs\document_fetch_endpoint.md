# Institute Document Fetch Endpoint

## Overview

New endpoint to fetch institute document binary data by document path, similar to how images are served. Returns base64 encoded document data that can be used directly in the frontend.

## Endpoint

```
GET /api/institutes/document/{document_path}
```

## Authentication

- **Required**: Institute user authentication
- **Headers**: `Authorization: Bearer <token>`

## Parameters

- **document_path** (path parameter): The relative path to the document
  - Example: `institute_documents/other/42c8449e_20250818_160411_627c221f.txt`
  - Example: `institute_documents/accreditation/abc123_certificate.pdf`

## Response Schema

```json
{
  "success": true,
  "document_path": "institute_documents/other/42c8449e_20250818_160411_627c221f.txt",
  "base64_data": "dGVzdCBmaWxlIGNvbnRlbnQ=",
  "mime_type": "text/plain",
  "size_bytes": 17,
  "data_url": "data:text/plain;base64,dGVzdCBmaWxlIGNvbnRlbnQ="
}
```

## Usage Examples

### JavaScript/Fetch
```javascript
async function fetchDocument(documentPath) {
  try {
    const response = await fetch(`/api/institutes/document/${documentPath}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      
      // Use the base64 data directly
      console.log('Document data:', data.base64_data);
      
      // Or use the data URL for display/download
      const link = document.createElement('a');
      link.href = data.data_url;
      link.download = getFilenameFromPath(data.document_path);
      link.click();
      
      return data;
    } else {
      throw new Error(`Failed to fetch document: ${response.status}`);
    }
  } catch (error) {
    console.error('Error fetching document:', error);
    throw error;
  }
}

// Usage
const documentData = await fetchDocument('institute_documents/other/42c8449e_20250818_160411_627c221f.txt');
```

### Python requests
```python
import requests
import base64

def fetch_document(document_path, token):
    url = f"https://edufair.duckdns.org/api/institutes/document/{document_path}"
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(url, headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        
        # Decode base64 to get original file content
        file_content = base64.b64decode(data['base64_data'])
        
        # Save to file
        filename = document_path.split('/')[-1]
        with open(filename, 'wb') as f:
            f.write(file_content)
        
        return data
    else:
        raise Exception(f"Failed to fetch document: {response.status_code}")

# Usage
document_data = fetch_document('institute_documents/other/42c8449e_20250818_160411_627c221f.txt', token)
```

### curl
```bash
curl -X GET "https://edufair.duckdns.org/api/institutes/document/institute_documents/other/42c8449e_20250818_160411_627c221f.txt" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Frontend Integration

### Display Document in Browser
```javascript
async function displayDocument(documentPath) {
  const data = await fetchDocument(documentPath);
  
  // Create blob from base64
  const byteCharacters = atob(data.base64_data);
  const byteNumbers = new Array(byteCharacters.length);
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }
  const byteArray = new Uint8Array(byteNumbers);
  const blob = new Blob([byteArray], { type: data.mime_type });
  
  // Create object URL for display
  const objectUrl = URL.createObjectURL(blob);
  
  // Display in iframe (for PDFs) or download
  if (data.mime_type === 'application/pdf') {
    const iframe = document.createElement('iframe');
    iframe.src = objectUrl;
    iframe.width = '100%';
    iframe.height = '600px';
    document.body.appendChild(iframe);
  } else {
    // For other files, trigger download
    const link = document.createElement('a');
    link.href = objectUrl;
    link.download = getFilenameFromPath(data.document_path);
    link.click();
  }
}
```

### Download Document
```javascript
async function downloadDocument(documentPath) {
  const data = await fetchDocument(documentPath);
  
  // Use the data URL for immediate download
  const link = document.createElement('a');
  link.href = data.data_url;
  link.download = getFilenameFromPath(data.document_path);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

function getFilenameFromPath(path) {
  return path.split('/').pop();
}
```

## Error Handling

### 404 - Document Not Found
```json
{
  "detail": "Document not found: institute_documents/other/nonexistent.txt"
}
```

### 500 - Server Error
```json
{
  "detail": "Error retrieving document: [error details]"
}
```

### 401 - Unauthorized
```json
{
  "detail": "Not authenticated"
}
```

## Document Path Format

Document paths follow this structure:
```
institute_documents/{document_type}/{unique_filename}
```

Where:
- **document_type**: `accreditation`, `license`, `certificate`, `other`
- **unique_filename**: Generated filename with timestamp and UUID

Examples:
- `institute_documents/accreditation/42c8449e_20250818_160411_certificate.pdf`
- `institute_documents/license/abc123_20250818_160500_license.pdf`
- `institute_documents/other/xyz789_20250818_160600_document.txt`

## Benefits

### ✅ **Simple API**
- Single endpoint for all document types
- Path-based document identification
- Standard REST approach

### ✅ **Flexible Response**
- Base64 data for direct use
- Data URL for immediate display/download
- MIME type for proper handling
- File size information

### ✅ **Frontend Friendly**
- Easy integration with JavaScript
- Works with any HTTP client
- Supports both display and download use cases

### ✅ **Secure**
- Requires authentication
- Only institute users can access
- Uses existing file storage service

This endpoint provides a clean way for the frontend to fetch document binary data just like images!
