# Document Management Behavior

## Overview

The institute profile update endpoint now properly handles document management based on what's included in the request body. This ensures the backend stays in sync with frontend document state.

## Document Management Logic

### 1. Document Deletion
**When**: A document exists in the database but is NOT included in the request body
**Action**: Document is deleted from both database and file storage
**Reason**: User removed the document from the frontend

```javascript
// Frontend removes a document
const currentDocuments = [doc1, doc2, doc3];
const updatedDocuments = [doc1, doc3]; // doc2 removed

// When sending request with updatedDocuments
// Backend will automatically delete doc2
```

### 2. Document Update/Replacement
**When**: A document with the same filename exists in both database and request body
**Action**: Old file is deleted, new file is saved, database record is updated
**Reason**: User replaced/updated an existing document

```javascript
// Frontend updates a document (same filename, new content)
const updatedDocument = {
  filename: "accreditation.pdf", // Same name
  data: "new_base64_content",    // New content
  document_type: "accreditation",
  description: "Updated certificate"
};
```

### 3. Document Addition
**When**: A document in the request body doesn't exist in the database
**Action**: New file is saved, new database record is created
**Reason**: User added a new document

```javascript
// Frontend adds a new document
const newDocument = {
  filename: "license.pdf",       // New filename
  data: "base64_content",
  document_type: "license",
  description: "Operating license"
};
```

## Implementation Details

### Backend Logic Flow

1. **Get existing documents** from database for the institute
2. **Extract document names** from the request body
3. **Identify documents to delete** (exist in DB but not in request)
4. **Delete removed documents** from file storage and database
5. **Process request documents**:
   - If document exists: Update (delete old file, save new file, update record)
   - If document is new: Create (save file, create new record)

### Code Example

```python
# Get existing documents
existing_documents = db.query(InstituteDocument).filter(
    InstituteDocument.institute_id == institute_id
).all()

# Get document names from request
request_document_names = [doc.filename for doc in request.documents]

# Find documents to delete
documents_to_delete = []
for existing_doc in existing_documents:
    if existing_doc.document_name not in request_document_names:
        documents_to_delete.append(existing_doc)

# Delete removed documents
for doc_to_delete in documents_to_delete:
    # Delete file from storage
    os.remove(os.path.join("uploads", doc_to_delete.document_url))
    # Delete from database
    db.delete(doc_to_delete)
```

## Frontend Integration

### Complete Document Management

```javascript
class DocumentManager {
  constructor() {
    this.documents = []; // Current document state
  }

  // Add new document
  addDocument(file, documentType, description) {
    const reader = new FileReader();
    reader.onload = (e) => {
      const base64Data = e.target.result.split(',')[1];
      
      this.documents.push({
        filename: file.name,
        content_type: file.type,
        data: base64Data,
        document_type: documentType,
        description: description
      });
      
      this.updateUI();
    };
    reader.readAsDataURL(file);
  }

  // Remove document
  removeDocument(filename) {
    this.documents = this.documents.filter(doc => doc.filename !== filename);
    this.updateUI();
  }

  // Update document
  updateDocument(filename, newData) {
    const docIndex = this.documents.findIndex(doc => doc.filename === filename);
    if (docIndex !== -1) {
      this.documents[docIndex] = { ...this.documents[docIndex], ...newData };
      this.updateUI();
    }
  }

  // Send to backend
  async saveProfile(profileData) {
    const requestData = {
      ...profileData,
      documents: this.documents // Only send current documents
    };

    const response = await fetch('/api/institutes/profile/with-documents', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(requestData)
    });

    return response.json();
  }
}
```

### Usage Example

```javascript
const docManager = new DocumentManager();

// Load existing documents from API
const profile = await fetchProfile();
docManager.documents = profile.documents || [];

// User adds a document
docManager.addDocument(file, 'accreditation', 'University certificate');

// User removes a document
docManager.removeDocument('old_license.pdf');

// User updates profile (documents are automatically managed)
await docManager.saveProfile({
  institute_name: 'Updated University',
  description: 'Updated description'
});
```

## Benefits

### ✅ **Automatic Cleanup**
- Removed documents are automatically deleted
- No orphaned files in storage
- Database stays clean

### ✅ **Seamless Updates**
- Replace documents with same filename
- Verification status resets on update
- Old files are properly cleaned up

### ✅ **Simple Frontend Logic**
- Frontend just sends current state
- No need to track additions/deletions
- Backend handles all the complexity

### ✅ **Data Integrity**
- File storage and database stay in sync
- Atomic operations with proper error handling
- Rollback on failures

## Error Handling

### File Deletion Errors
- If file deletion fails, operation continues
- Error is logged but doesn't stop the process
- Database cleanup still happens

### Document Processing Errors
- Individual document errors don't affect others
- Clear error messages for debugging
- Uploaded files are cleaned up on failure

### Transaction Safety
- Database operations are wrapped in transactions
- Rollback on any critical failure
- File cleanup on transaction rollback

This approach ensures that the backend document state always matches what the user sees in the frontend!
