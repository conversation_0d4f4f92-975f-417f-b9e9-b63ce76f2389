#!/usr/bin/env python3
"""
Test that admin institute endpoint returns complete document objects
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastapi.testclient import Test<PERSON>lient
from main import app
from config.session import Session<PERSON>ocal
from Models.users import User, InstituteDocument

# Create test client
client = TestClient(app)

def get_admin_token():
    """Get admin token for testing"""
    from config.security import create_access_token

    db = SessionLocal()
    try:
        # Find an admin user or create one for testing
        admin_user = db.query(User).filter(User.user_type == 'admin').first()
        if admin_user:
            token = create_access_token(data={"sub": admin_user.email})
            return token, admin_user.id
        else:
            print("❌ No admin user found in database")
            return None, None
    finally:
        db.close()

def get_institute_with_documents():
    """Find an institute that has documents"""
    db = SessionLocal()
    try:
        # Find an institute user that has documents
        institute_with_docs = db.query(User).join(InstituteDocument, User.id == InstituteDocument.institute_id).filter(
            User.user_type == 'institute'
        ).first()
        
        if institute_with_docs:
            return institute_with_docs.id
        else:
            print("❌ No institute with documents found")
            return None
    finally:
        db.close()

def test_admin_get_institute_documents():
    """Test that admin GET institute endpoint returns complete document objects"""
    print("🧪 Testing admin GET institute endpoint for complete document objects...")
    
    # Get admin token
    admin_token, admin_id = get_admin_token()
    if not admin_token:
        print("❌ Could not get admin token")
        return False
    
    # Get institute with documents
    institute_id = get_institute_with_documents()
    if not institute_id:
        print("❌ Could not find institute with documents")
        return False
    
    headers = {"Authorization": f"Bearer {admin_token}"}
    
    # Test the admin institute endpoint
    response = client.get(f"/api/institutes/admin/institute/{institute_id}", headers=headers)
    
    print(f"📋 Response status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        profile = data.get('profile', {})
        documents = profile.get('documents', [])
        
        print(f"✅ Admin GET institute successful")
        print(f"📋 Institute name: {profile.get('institute_name')}")
        print(f"📋 Documents count: {len(documents)}")
        
        if documents:
            print("📋 Document objects structure:")
            first_doc = documents[0]
            
            # Check if we have complete document objects
            expected_fields = ['id', 'institute_id', 'document_type', 'document_url', 
                             'document_name', 'description', 'created_at', 'verified', 
                             'verified_at', 'verified_by']
            
            missing_fields = []
            present_fields = []
            
            for field in expected_fields:
                if field in first_doc:
                    present_fields.append(field)
                    print(f"   ✅ {field}: {type(first_doc[field]).__name__}")
                else:
                    missing_fields.append(field)
                    print(f"   ❌ Missing: {field}")
            
            if not missing_fields:
                print("✅ All expected document fields are present!")
                print("✅ Documents are complete objects, not just names/URLs")
                return True
            else:
                print(f"❌ Missing fields: {missing_fields}")
                return False
        else:
            print("⚠️  No documents found for this institute")
            return False
    else:
        print(f"❌ Admin GET institute failed: {response.status_code}")
        print(f"Response: {response.text}")
        return False

def main():
    """Main test function"""
    print("🚀 Testing admin institute endpoint document objects...")
    print("=" * 60)
    
    success = test_admin_get_institute_documents()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ SUCCESS: Admin endpoint returns complete document objects!")
        print("🎉 Documents include all fields: id, institute_id, document_type, document_url,")
        print("   document_name, description, created_at, verified, verified_at, verified_by")
        return 0
    else:
        print("❌ FAILED: Admin endpoint does not return complete document objects")
        return 1

if __name__ == "__main__":
    exit(main())
