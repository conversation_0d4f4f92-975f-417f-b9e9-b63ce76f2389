#!/usr/bin/env python3
"""
Test the institute profile with documents API endpoint
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncio
from fastapi.testclient import <PERSON><PERSON>lient
from main import app
from config.session import get_db
from Models.users import User, UserTypeEnum
from sqlalchemy.orm import Session
import uuid
import tempfile
import io

# Create test client
client = TestClient(app)

def create_test_institute_user():
    """Create a test institute user and return auth token"""
    from config.session import SessionLocal

    db = SessionLocal()
    try:
        # Check if test institute already exists
        test_email = "<EMAIL>"
        existing_user = db.query(User).filter(User.email == test_email).first()

        if existing_user:
            print(f"✅ Using existing test institute user: {existing_user.id}")
            user_id = existing_user.id
        else:
            # Create a new test institute user manually
            import uuid
            from config.security import get_password_hash

            user_id = uuid.uuid4()
            hashed_password = get_password_hash("testpassword123")

            new_user = User(
                id=user_id,
                username="test_institute",
                email=test_email,
                mobile="+1234567890",
                password_hash=hashed_password,
                country="US",
                user_type=UserTypeEnum.institute,
                is_email_verified=True,
                is_mobile_verified=True
            )

            db.add(new_user)
            db.commit()
            db.refresh(new_user)
            print(f"✅ Created new test institute user: {user_id}")

        # Generate auth token
        from config.security import create_access_token
        token = create_access_token(data={"sub": test_email})

        return token, user_id

    except Exception as e:
        print(f"❌ Error creating test institute user: {e}")
        db.rollback()
        return None, None
    finally:
        db.close()

def create_test_file():
    """Create a test PDF file for upload"""
    # Create a simple test file content
    content = b"This is a test PDF document for institute verification."
    
    # Create a temporary file-like object
    file_obj = io.BytesIO(content)
    file_obj.name = "test_document.pdf"
    
    return file_obj

def test_profile_update_with_documents():
    """Test the profile update with documents endpoint"""
    print("🧪 Testing institute profile update with documents...")
    
    # Create test user and get token
    token, user_id = create_test_institute_user()
    if not token:
        return False
    
    # Create test file
    test_file = create_test_file()
    
    # Prepare the request data
    headers = {"Authorization": f"Bearer {token}"}
    
    # Test data
    form_data = {
        "institute_name": "Test University",
        "description": "A test educational institution",
        "city": "Test City",
        "state": "Test State",
        "institute_type": "university",
        "established_year": 2000,
        "document_types": ["accreditation"],
        "document_descriptions": ["Test accreditation document"]
    }
    
    files = {
        "document_files": ("test_document.pdf", test_file, "application/pdf")
    }
    
    try:
        # Make the API request
        response = client.put(
            "/api/institutes/profile/with-documents",
            headers=headers,
            data=form_data,
            files=files
        )
        
        print(f"📋 Response status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ API request successful!")
            response_data = response.json()
            print(f"📋 Institute name: {response_data.get('profile', {}).get('institute_name')}")

            # Check if documents were uploaded
            documents = response_data.get('profile', {}).get('documents', [])
            print(f"📋 Documents uploaded: {len(documents)}")

            if documents:
                for doc in documents:
                    print(f"   - {doc.get('document_name')} ({doc.get('document_type')})")

            return True
        else:
            print(f"❌ API request failed with status {response.status_code}")
            print(f"📋 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing API: {e}")
        return False

def test_profile_update_without_documents():
    """Test the profile update without documents (should still work)"""
    print("🧪 Testing institute profile update without documents...")
    
    # Create test user and get token
    token, user_id = create_test_institute_user()
    if not token:
        return False
    
    # Prepare the request data
    headers = {"Authorization": f"Bearer {token}"}
    
    # Test data (no documents)
    form_data = {
        "institute_name": "Test University Updated",
        "description": "An updated test educational institution",
        "city": "Updated City",
        "state": "Updated State",
        "institute_type": "college",
        "established_year": 1995
    }
    
    try:
        # Make the API request
        response = client.put(
            "/api/institutes/profile/with-documents",
            headers=headers,
            data=form_data
        )
        
        print(f"📋 Response status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ API request successful!")
            response_data = response.json()
            print(f"📋 Institute name: {response_data.get('profile', {}).get('institute_name')}")
            return True
        else:
            print(f"❌ API request failed with status {response.status_code}")
            print(f"📋 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing API: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Starting institute API tests...")
    print("=" * 60)
    
    success_count = 0
    total_tests = 2
    
    # Test 1: Profile update with documents
    if test_profile_update_with_documents():
        success_count += 1
    
    print("\n" + "-" * 40)
    
    # Test 2: Profile update without documents
    if test_profile_update_without_documents():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {success_count}/{total_tests} tests passed")
    
    if success_count == total_tests:
        print("✅ All tests passed! The API is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    exit(main())
