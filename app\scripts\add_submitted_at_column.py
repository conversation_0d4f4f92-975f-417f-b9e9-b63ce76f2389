#!/usr/bin/env python3
"""
Add submitted_at column to institute_profile table
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from config.session import engine

def add_submitted_at_column():
    """Add submitted_at column to institute_profile table"""
    try:
        with engine.connect() as connection:
            # Check if column already exists
            result = connection.execute(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'institute_profile' 
                AND column_name = 'submitted_at'
            """))
            
            if result.fetchone():
                print("✅ submitted_at column already exists")
                return True
            
            # Add the column
            connection.execute(text("""
                ALTER TABLE institute_profile 
                ADD COLUMN submitted_at TIMESTAMP WITH TIME ZONE
            """))
            
            connection.commit()
            print("✅ Successfully added submitted_at column to institute_profile table")
            return True
            
    except Exception as e:
        print(f"❌ Error adding submitted_at column: {e}")
        return False

def main():
    """Main function"""
    print("🔧 Adding submitted_at column to institute_profile table...")
    
    if add_submitted_at_column():
        print("✅ Database migration completed successfully!")
        return 0
    else:
        print("❌ Database migration failed!")
        return 1

if __name__ == "__main__":
    exit(main())
