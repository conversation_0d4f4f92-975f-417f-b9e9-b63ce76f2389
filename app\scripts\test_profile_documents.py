#!/usr/bin/env python3
"""
Test that institute profile GET endpoints properly include documents
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastapi.testclient import Test<PERSON><PERSON>
from main import app
from config.session import SessionLocal
from Models.users import User, InstituteDocument
import json

# Create test client
client = TestClient(app)

def get_test_user_token():
    """Get auth token for test user"""
    from config.security import create_access_token
    
    db = SessionLocal()
    try:
        test_email = "<EMAIL>"
        user = db.query(User).filter(User.email == test_email).first()
        if user:
            token = create_access_token(data={"sub": test_email})
            return token, user.id
    finally:
        db.close()
    return None, None

def test_profile_get_includes_documents():
    """Test that GET /profile includes documents"""
    token, user_id = get_test_user_token()
    if not token:
        print("❌ Could not get test user token")
        return False
    
    headers = {"Authorization": f"Bearer {token}"}
    
    print("🧪 Testing GET /api/institutes/profile includes documents...")
    
    # First, check how many documents exist in database
    db = SessionLocal()
    try:
        documents_count = db.query(InstituteDocument).filter(
            InstituteDocument.institute_id == user_id
        ).count()
        print(f"📋 Documents in database: {documents_count}")
    finally:
        db.close()
    
    # Test the profile endpoint
    response = client.get("/api/institutes/profile", headers=headers)
    
    print(f"📋 Response status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print("✅ Profile GET successful!")
        
        # Check if profile data exists
        profile = data.get('profile', {})
        print(f"📋 Institute name: {profile.get('institute_name')}")
        
        # Check if documents are included
        documents = profile.get('documents', [])
        print(f"📋 Documents in response: {len(documents)}")
        
        if documents:
            print("📋 Document details:")
            for i, doc in enumerate(documents):
                print(f"   {i+1}. {doc.get('document_name')} ({doc.get('document_type')})")
                print(f"      Created: {doc.get('created_at')}")
                print(f"      Verified: {doc.get('verified')}")
        
        # Verify documents count matches
        if len(documents) == documents_count:
            print("✅ Document count matches database!")
            return True
        else:
            print(f"❌ Document count mismatch: API returned {len(documents)}, DB has {documents_count}")
            return False
    else:
        print(f"❌ Request failed: {response.text}")
        return False

def test_admin_list_endpoints():
    """Test admin list endpoints are lightweight"""
    # Create admin token (using test user for now)
    token, user_id = get_test_user_token()
    if not token:
        print("❌ Could not get test user token")
        return False
    
    headers = {"Authorization": f"Bearer {token}"}
    
    print("🧪 Testing admin list endpoints...")
    
    # Test regular list endpoint
    print("\n--- Testing GET /api/institutes/admin/all ---")
    response = client.get("/api/institutes/admin/all?size=5", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        print("✅ Institute list successful!")
        print(f"📋 Total institutes: {data.get('total')}")
        print(f"📋 Returned institutes: {len(data.get('institutes', []))}")
        
        # Check what fields are included
        if data.get('institutes'):
            institute = data['institutes'][0]
            print("📋 Fields included in list:")
            for key, value in institute.items():
                print(f"   - {key}: {type(value).__name__}")
    else:
        print(f"❌ Institute list failed: {response.text}")
        return False
    
    # Test minimal list endpoint
    print("\n--- Testing GET /api/institutes/list/minimal ---")
    response = client.get("/api/institutes/list/minimal?limit=5", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        print("✅ Minimal institute list successful!")
        print(f"📋 Returned institutes: {len(data)}")
        
        # Check what fields are included
        if data:
            institute = data[0]
            print("📋 Fields included in minimal list:")
            for key, value in institute.items():
                print(f"   - {key}: {type(value).__name__}")
        
        return True
    else:
        print(f"❌ Minimal institute list failed: {response.text}")
        return False

def test_verification_list_endpoint():
    """Test verification list endpoint"""
    token, user_id = get_test_user_token()
    if not token:
        print("❌ Could not get test user token")
        return False
    
    headers = {"Authorization": f"Bearer {token}"}
    
    print("🧪 Testing verification list endpoint...")
    
    response = client.get("/api/institutes/admin/pending-verification", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        print("✅ Verification list successful!")
        print(f"📋 Pending verification: {len(data)}")
        
        if data:
            institute = data[0]
            print("📋 Fields included in verification list:")
            for key, value in institute.items():
                print(f"   - {key}: {type(value).__name__}")
            
            # Check if documents_count is included
            if 'documents_count' in institute:
                print(f"📋 Documents count: {institute['documents_count']}")
            else:
                print("⚠️  documents_count field missing")
        
        return True
    else:
        print(f"❌ Verification list failed: {response.text}")
        return False

def test_profile_status_with_documents():
    """Test that profile status shows document information"""
    token, user_id = get_test_user_token()
    if not token:
        print("❌ Could not get test user token")
        return False
    
    headers = {"Authorization": f"Bearer {token}"}
    
    print("🧪 Testing profile status endpoint...")
    
    response = client.get("/api/institutes/profile/status", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        print("✅ Profile status successful!")
        print(f"📋 Profile exists: {data.get('profile_exists')}")
        print(f"📋 Profile complete: {data.get('profile_complete')}")
        print(f"📋 Verification status: {data.get('verification_status')}")
        
        # Check UI actions
        ui_actions = data.get('ui_actions', {})
        primary_btn = ui_actions.get('primary_button', {})
        secondary_btn = ui_actions.get('secondary_button')
        
        print(f"📋 Primary button: {primary_btn.get('text')} ({'enabled' if primary_btn.get('enabled') else 'disabled'})")
        if secondary_btn:
            print(f"📋 Secondary button: {secondary_btn.get('text')} ({'enabled' if secondary_btn.get('enabled') else 'disabled'})")
        
        return True
    else:
        print(f"❌ Profile status failed: {response.text}")
        return False

def main():
    """Main test function"""
    print("🚀 Testing institute profile document inclusion and lightweight lists...")
    print("=" * 70)
    
    success_count = 0
    total_tests = 4
    
    # Test 1: Profile GET includes documents
    if test_profile_get_includes_documents():
        success_count += 1
    
    print("\n" + "-" * 50)
    
    # Test 2: Admin list endpoints are lightweight
    if test_admin_list_endpoints():
        success_count += 1
    
    print("\n" + "-" * 50)
    
    # Test 3: Verification list endpoint
    if test_verification_list_endpoint():
        success_count += 1
    
    print("\n" + "-" * 50)
    
    # Test 4: Profile status endpoint
    if test_profile_status_with_documents():
        success_count += 1
    
    print("\n" + "=" * 70)
    print(f"📊 Test Results: {success_count}/{total_tests} tests passed")
    
    if success_count == total_tests:
        print("✅ All tests passed!")
        print("\n📋 Summary:")
        print("   ✅ Profile GET includes documents for both admin and self views")
        print("   ✅ Admin list endpoints are lightweight with essential fields only")
        print("   ✅ Verification list includes document counts instead of full documents")
        print("   ✅ Profile status provides clear UI guidance")
        return 0
    else:
        print("❌ Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    exit(main())
