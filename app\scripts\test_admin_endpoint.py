#!/usr/bin/env python3
"""
Test the new admin endpoint for frontend compatibility
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastapi.testclient import TestClient
from main import app

# Create test client
client = TestClient(app)

def test_admin_pending_endpoint():
    """Test the new admin pending institutes endpoint"""
    print("🧪 Testing new admin endpoint: GET /api/admin/institutes/pending")
    
    # Test without authentication (should fail)
    response = client.get("/api/admin/institutes/pending")
    print(f"📋 Without auth - Status: {response.status_code}")
    
    if response.status_code == 401:
        print("✅ Properly requires authentication")
    else:
        print(f"⚠️  Expected 401, got {response.status_code}")
    
    # Test with institute user (should fail with 403)
    from config.security import create_access_token
    from config.session import SessionLocal
    from Models.users import User
    
    db = SessionLocal()
    try:
        test_email = "<EMAIL>"
        user = db.query(User).filter(User.email == test_email).first()
        if user:
            token = create_access_token(data={"sub": test_email})
            headers = {"Authorization": f"Bearer {token}"}
            
            response = client.get("/api/admin/institutes/pending", headers=headers)
            print(f"📋 With institute user - Status: {response.status_code}")
            
            if response.status_code == 403:
                print("✅ Properly requires admin permissions")
                return True
            else:
                print(f"⚠️  Expected 403, got {response.status_code}")
                print(f"Response: {response.text}")
                return False
        else:
            print("❌ Test user not found")
            return False
    finally:
        db.close()

def test_endpoint_structure():
    """Test that the endpoint is properly registered"""
    print("\n🧪 Testing endpoint registration...")
    
    # Check if the route exists in the app
    routes = []
    for route in app.routes:
        if hasattr(route, 'path'):
            routes.append(route.path)
    
    expected_route = "/api/admin/institutes/pending"
    if expected_route in routes:
        print(f"✅ Route {expected_route} is registered")
        return True
    else:
        print(f"❌ Route {expected_route} not found")
        print("Available routes:")
        for route in routes:
            if 'admin' in route or 'institute' in route:
                print(f"   - {route}")
        return False

def main():
    """Main test function"""
    print("🚀 Testing admin endpoint for frontend compatibility...")
    print("=" * 60)
    
    success_count = 0
    total_tests = 2
    
    # Test 1: Endpoint structure
    if test_endpoint_structure():
        success_count += 1
    
    print("\n" + "-" * 40)
    
    # Test 2: Endpoint functionality
    if test_admin_pending_endpoint():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {success_count}/{total_tests} tests passed")
    
    if success_count == total_tests:
        print("✅ Admin endpoint is working correctly!")
        print("🚀 Frontend should now be able to access: GET /api/admin/institutes/pending")
        return 0
    else:
        print("❌ Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    exit(main())
