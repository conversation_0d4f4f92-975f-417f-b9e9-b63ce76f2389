<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Institute Profile Management Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .status-message {
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        
        .status-pending { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .status-verified { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status-rejected { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status-under_review { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        
        .progress-container {
            margin-bottom: 25px;
        }
        
        .progress-bar {
            width: 100%;
            height: 25px;
            background-color: #e9ecef;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
            border: 1px solid #dee2e6;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            transition: width 0.5s ease;
            border-radius: 12px;
        }
        
        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 13px;
            font-weight: bold;
            color: #495057;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            margin-bottom: 25px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: bold;
            font-size: 14px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            min-width: 140px;
            text-align: center;
        }
        
        .btn-primary { background-color: #007bff; color: white; }
        .btn-secondary { background-color: #6c757d; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: #212529; }
        
        .btn:hover:not(.disabled) { 
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .btn.disabled { 
            opacity: 0.5; 
            cursor: not-allowed;
            transform: none !important;
            box-shadow: none !important;
        }
        
        .missing-fields {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #f5c6cb;
            margin-bottom: 20px;
        }
        
        .missing-fields h4 {
            margin-top: 0;
            margin-bottom: 10px;
        }
        
        .missing-fields ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .demo-controls {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
        }
        
        .demo-controls h3 {
            margin-top: 0;
            color: #495057;
        }
        
        .demo-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .demo-btn {
            padding: 8px 16px;
            background-color: #6c757d;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .demo-btn:hover {
            background-color: #5a6268;
        }
        
        .verification-info {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 6px;
            margin-top: 20px;
        }
        
        .verification-info h4 {
            margin-top: 0;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏫 Institute Profile Management</h1>
        
        <!-- Demo Controls -->
        <div class="demo-controls">
            <h3>Demo Controls (Simulate Different States)</h3>
            <div class="demo-buttons">
                <button class="demo-btn" onclick="simulateState('not_created')">No Profile</button>
                <button class="demo-btn" onclick="simulateState('incomplete')">Incomplete Profile</button>
                <button class="demo-btn" onclick="simulateState('complete')">Complete Profile</button>
                <button class="demo-btn" onclick="simulateState('under_review')">Under Review</button>
                <button class="demo-btn" onclick="simulateState('verified')">Verified</button>
                <button class="demo-btn" onclick="simulateState('rejected')">Rejected</button>
            </div>
        </div>
        
        <!-- Status Message -->
        <div id="statusMessage" class="status-message"></div>
        
        <!-- Progress Bar -->
        <div class="progress-container">
            <div class="progress-bar">
                <div id="progressFill" class="progress-fill"></div>
                <div id="progressText" class="progress-text"></div>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="action-buttons">
            <button id="primaryButton" class="btn"></button>
            <button id="secondaryButton" class="btn" style="display: none;"></button>
        </div>
        
        <!-- Missing Fields Alert -->
        <div id="missingFields" class="missing-fields" style="display: none;">
            <h4>Required Fields Missing:</h4>
            <ul id="missingFieldsList"></ul>
        </div>
        
        <!-- Verification Info -->
        <div class="verification-info">
            <h4>Current Status Information</h4>
            <p><strong>Profile Exists:</strong> <span id="profileExists">-</span></p>
            <p><strong>Profile Complete:</strong> <span id="profileComplete">-</span></p>
            <p><strong>Verification Status:</strong> <span id="verificationStatus">-</span></p>
            <p><strong>Can Submit for Verification:</strong> <span id="canSubmit">-</span></p>
        </div>
    </div>

    <script>
        // Sample data for different states
        const sampleStates = {
            not_created: {
                profile_exists: false,
                profile_complete: false,
                verification_status: "not_created",
                completion_percentage: 0,
                missing_fields: ["institute_name", "description", "address", "city", "state", "established_year", "institute_type"],
                can_submit_for_verification: false,
                ui_actions: {
                    primary_button: {
                        text: "Create Profile",
                        action: "create_profile",
                        enabled: true
                    },
                    secondary_button: null,
                    status_message: "Welcome! Let's create your institute profile to get started."
                }
            },
            incomplete: {
                profile_exists: true,
                profile_complete: false,
                verification_status: "pending",
                completion_percentage: 71.4,
                missing_fields: ["address", "established_year"],
                can_submit_for_verification: false,
                ui_actions: {
                    primary_button: {
                        text: "Update Profile",
                        action: "update_profile",
                        enabled: true
                    },
                    secondary_button: {
                        text: "Send for Approval",
                        action: "submit_for_verification",
                        enabled: false,
                        disabled_reason: "Complete 2 missing field(s) first",
                        style: "secondary"
                    },
                    status_message: "Complete your profile (71.4% done)"
                }
            },
            complete: {
                profile_exists: true,
                profile_complete: true,
                verification_status: "pending",
                completion_percentage: 100,
                missing_fields: [],
                can_submit_for_verification: true,
                ui_actions: {
                    primary_button: {
                        text: "Update Profile",
                        action: "update_profile",
                        enabled: true
                    },
                    secondary_button: {
                        text: "Send for Approval",
                        action: "submit_for_verification",
                        enabled: true,
                        style: "success"
                    },
                    status_message: "Your profile is complete and ready for verification!"
                }
            },
            under_review: {
                profile_exists: true,
                profile_complete: true,
                verification_status: "under_review",
                completion_percentage: 100,
                missing_fields: [],
                can_submit_for_verification: false,
                ui_actions: {
                    primary_button: {
                        text: "Update Profile",
                        action: "update_profile",
                        enabled: false,
                        disabled_reason: "Profile is currently under review"
                    },
                    secondary_button: null,
                    status_message: "Your profile is currently under review by our team."
                }
            },
            verified: {
                profile_exists: true,
                profile_complete: true,
                verification_status: "verified",
                completion_percentage: 100,
                missing_fields: [],
                can_submit_for_verification: false,
                ui_actions: {
                    primary_button: {
                        text: "Update Profile",
                        action: "update_profile",
                        enabled: true
                    },
                    secondary_button: null,
                    status_message: "Your profile is verified and active!"
                }
            },
            rejected: {
                profile_exists: true,
                profile_complete: true,
                verification_status: "rejected",
                completion_percentage: 100,
                missing_fields: [],
                can_submit_for_verification: true,
                ui_actions: {
                    primary_button: {
                        text: "Update Profile",
                        action: "update_profile",
                        enabled: true
                    },
                    secondary_button: {
                        text: "Resubmit for Approval",
                        action: "submit_for_verification",
                        enabled: true,
                        style: "warning"
                    },
                    status_message: "Profile was rejected: Please update your institute description with more details."
                }
            }
        };

        function simulateState(stateName) {
            const state = sampleStates[stateName];
            updateUI(state);
        }

        function updateUI(data) {
            // Update status message
            const statusMessage = document.getElementById('statusMessage');
            statusMessage.textContent = data.ui_actions.status_message;
            statusMessage.className = `status-message status-${data.verification_status}`;

            // Update progress bar
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            progressFill.style.width = `${data.completion_percentage}%`;
            progressText.textContent = `${data.completion_percentage}% Complete`;

            // Update primary button
            const primaryButton = document.getElementById('primaryButton');
            const primaryAction = data.ui_actions.primary_button;
            primaryButton.textContent = primaryAction.text;
            primaryButton.className = `btn btn-primary ${!primaryAction.enabled ? 'disabled' : ''}`;
            primaryButton.disabled = !primaryAction.enabled;
            primaryButton.title = primaryAction.disabled_reason || '';

            // Update secondary button
            const secondaryButton = document.getElementById('secondaryButton');
            const secondaryAction = data.ui_actions.secondary_button;
            if (secondaryAction) {
                secondaryButton.style.display = 'inline-block';
                secondaryButton.textContent = secondaryAction.text;
                secondaryButton.className = `btn btn-${secondaryAction.style || 'secondary'} ${!secondaryAction.enabled ? 'disabled' : ''}`;
                secondaryButton.disabled = !secondaryAction.enabled;
                secondaryButton.title = secondaryAction.disabled_reason || '';
            } else {
                secondaryButton.style.display = 'none';
            }

            // Update missing fields
            const missingFieldsDiv = document.getElementById('missingFields');
            const missingFieldsList = document.getElementById('missingFieldsList');
            if (data.missing_fields.length > 0) {
                missingFieldsDiv.style.display = 'block';
                missingFieldsList.innerHTML = data.missing_fields.map(field => 
                    `<li>${field.replace('_', ' ').toUpperCase()}</li>`
                ).join('');
            } else {
                missingFieldsDiv.style.display = 'none';
            }

            // Update verification info
            document.getElementById('profileExists').textContent = data.profile_exists ? 'Yes' : 'No';
            document.getElementById('profileComplete').textContent = data.profile_complete ? 'Yes' : 'No';
            document.getElementById('verificationStatus').textContent = data.verification_status.replace('_', ' ').toUpperCase();
            document.getElementById('canSubmit').textContent = data.can_submit_for_verification ? 'Yes' : 'No';
        }

        // Initialize with incomplete profile state
        simulateState('incomplete');

        // Add click handlers for buttons
        document.getElementById('primaryButton').addEventListener('click', function() {
            if (!this.disabled) {
                alert('Primary action clicked: ' + this.textContent);
            }
        });

        document.getElementById('secondaryButton').addEventListener('click', function() {
            if (!this.disabled) {
                alert('Secondary action clicked: ' + this.textContent);
            }
        });
    </script>
</body>
</html>
